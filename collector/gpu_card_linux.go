package collector

import (
	"context"
	"log/slog"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

func init() {
	registerCollector("gpuhour", defaultEnabled, NewGPUTotalHoursCollector)
}

var (
	GpucardUpdateDuration  = 5 * time.Minute
	GpucardCleanDuration   = GpucardUpdateDuration * 4
	GpucardExpiredDuration = GpucardUpdateDuration * 3
	UserNameSpace          = "hero-user"
	MatchedResourceName    = "nvidia.com"
	NodeName               = os.Getenv("NODE_NAME")
	MetricsBuffer          = MetricsData{Metrics: make(map[int64]*Metric)}
	GPUHOUR                = "gpuhour"
)

type Metric struct {
	Buffer      []prometheus.Metric
	ExpiredTime time.Time
}

type MetricsData struct {
	Metrics map[int64]*Metric
	mtx     sync.Mutex
}

type ResourceStats struct {
	ResourceCount int64
	PodGpuHour    float64
}

type NodeResourceStats struct {
	Pods         map[string]*ResourceStats
	NodeGpuHour  float64
	ResourceName string
}

type GPUTotalHoursCollector struct {
	logger        *slog.Logger
	clientset     *kubernetes.Clientset
	desc          *prometheus.Desc
	subsystem     string
	podGpuStats   NodeResourceStats
	nodeName      string
	resourceName  string
	collectTicker *time.Ticker
	cleanupTicker *time.Ticker
}

func NewGPUTotalHoursCollector(logger *slog.Logger) (Collector, error) {
	config, err := rest.InClusterConfig()
	if err != nil {
		logger.Error("Error building kubeconfig", "err", err)
		return nil, err
	}
	K8sClientSet := kubernetes.NewForConfigOrDie(config)

	c := &GPUTotalHoursCollector{
		clientset: K8sClientSet,
		desc: prometheus.NewDesc(
			"leinao_node_gpu_time_total",
			"node gpu hour total",
			[]string{"node", "resource"}, nil,
		),
		subsystem:     "gpuhour",
		podGpuStats:   NodeResourceStats{Pods: make(map[string]*ResourceStats)},
		collectTicker: time.NewTicker(GpucardUpdateDuration),
		cleanupTicker: time.NewTicker(GpucardCleanDuration),
	}
	nodeName := os.Getenv("NODE_NAME")
	if nodeName == "" {
		if nodeName, err = os.Hostname(); err != nil {
			return nil, err
		}
	}

	return c, nil
}

func (g *GPUTotalHoursCollector) getNodeResourceName() string {
	node, err := g.clientset.CoreV1().Nodes().Get(context.TODO(), g.nodeName, metav1.GetOptions{})
	if err != nil {
		g.logger.Error("Error getting node status %s: %v", g.nodeName, err)
	}
	for resourceName, quantity := range node.Status.Allocatable {
		if strings.Contains(resourceName.String(), MatchedResourceName) && quantity.Value() > 0 {
			return resourceName.String()
		}
	}
	return "nocard"
}

func (g *GPUTotalHoursCollector) Update(ch chan<- prometheus.Metric) error {
	listOptions := metav1.ListOptions{
		FieldSelector: "spec.nodeName=" + NodeName,
	}

	pods, err := g.clientset.CoreV1().Pods(UserNameSpace).List(context.TODO(), listOptions)
	if err != nil {
		g.logger.Error("Error listing pods: %v", err)
	}
	g.getGPUCountByNode(pods.Items, GpucardUpdateDuration.Hours())
	g.syncNodeTimeHour()

	if g.podGpuStats.ResourceName == "" {
		g.podGpuStats.ResourceName = g.getNodeResourceName()
	}
	MetricsBuffer.mtx.Lock()
	defer MetricsBuffer.mtx.Unlock()

	tempMetric := &Metric{ExpiredTime: tc.Add(GpucardExpiredDuration)}
	tempMetric.Buffer = append(tempMetric.Buffer,
		prometheus.NewMetricWithTimestamp(tc, prometheus.MustNewConstMetric(g.desc, prometheus.CounterValue, g.podGpuStats.NodeGpuHour, NodeName, g.podGpuStats.ResourceName)))
	MetricsBuffer.Metrics[tc.Unix()] = tempMetric
}

func (g *GPUTotalHoursCollector) Run() error {

	g.logger.Info("collecter: %s run start", GPUHOUR)
	for tc := range g.collectTicker.C {
		go func(tc time.Time) {
			g.Update(tc)
		}(tc)
	}
	return nil
}

func (g *GPUTotalHoursCollector) Clean() {
	g.logger.Info("collecter: %s clean start", GPUHOUR)
	for tc := range g.cleanupTicker.C {
		MetricsBuffer.mtx.Lock()
		for bb, metric := range MetricsBuffer.Metrics {
			expiredtime := metric.ExpiredTime
			if expiredtime.Before(tc) {
				g.logger.Info("collecter: %s clean buffer timestamp %d", GPUHOUR, bb)
				delete(MetricsBuffer.Metrics, bb)
			}
		}
		MetricsBuffer.mtx.Unlock()
	}
}

func (g *GPUTotalHoursCollector) syncNodeTimeHour() {
	for _, podStats := range g.podGpuStats.Pods {
		g.podGpuStats.NodeGpuHour += podStats.PodGpuHour
	}
}

func (g *GPUTotalHoursCollector) getGPUCountByNode(pods []corev1.Pod, duration float64) {
	for _, pod := range pods {
		switch pod.Status.Phase {
		case corev1.PodRunning:
			for _, container := range pod.Spec.Containers {
				for resourceName, quantity := range container.Resources.Requests {
					if strings.Contains(resourceName.String(), MatchedResourceName) {
						if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
							g.podGpuStats.Pods[pod.Name].PodGpuHour = duration * float64(quantity.Value())
						} else {
							g.podGpuStats.ResourceName = string(resourceName)
							g.podGpuStats.Pods[pod.Name] = &ResourceStats{
								ResourceCount: quantity.Value(),
							}
						}

					}
				}
			}
		case corev1.PodFailed:
			if _, exists := g.podGpuStats.Pods[pod.Name]; exists {
				delete(g.podGpuStats.Pods, pod.Name)
			}
		case corev1.PodPending:

		}
	}

}
